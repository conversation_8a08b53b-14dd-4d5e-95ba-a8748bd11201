<template>
  <div class="media-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Picture /></el-icon>
            媒体管理
          </h1>
          <p class="page-description">管理图片、视频、音频和文档等媒体文件</p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Upload"
            @click="handleUploadFiles"
            v-permission="'media.upload'"
          >
            上传文件
          </el-button>
          <el-button
            :icon="Download"
            @click="handleExportMedia"
            v-permission="'media.read'"
          >
            导出数据
          </el-button>
          <el-dropdown @command="handleBatchOperation" v-if="selectedMedia.length > 0">
            <el-button :icon="Operation">
              批量操作 <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="download">批量下载</el-dropdown-item>
                <el-dropdown-item command="public">设为公开</el-dropdown-item>
                <el-dropdown-item command="private">设为私有</el-dropdown-item>
                <el-dropdown-item command="delete" class="danger">批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Files /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ mediaStats?.totalFiles || 0 }}</div>
                <div class="stats-label">总文件数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon images">
                <el-icon><Picture /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ mediaStats?.imageCount || 0 }}</div>
                <div class="stats-label">图片文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon videos">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ mediaStats?.videoCount || 0 }}</div>
                <div class="stats-label">视频文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon audios">
                <el-icon><Headset /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ mediaStats?.audioCount || 0 }}</div>
                <div class="stats-label">音频文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon documents">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ mediaStats?.documentCount || 0 }}</div>
                <div class="stats-label">文档文件</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon storage">
                <el-icon><Coin /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ formatFileSize(mediaStats?.totalSize || 0) }}</div>
                <div class="stats-label">存储空间</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索区域 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-content">
        <el-form :model="filterForm" inline class="filter-form">
          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.search"
              placeholder="搜索文件名、描述或标签"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 280px"
            />
          </el-form-item>

          <el-form-item label="类型">
            <el-select
              v-model="filterForm.category"
              placeholder="选择文件类型"
              clearable
              @change="handleFilter"
              style="width: 140px"
            >
              <el-option label="图片" value="image" />
              <el-option label="视频" value="video" />
              <el-option label="音频" value="audio" />
              <el-option label="文档" value="document" />
            </el-select>
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="filterForm.isPublic"
              placeholder="选择公开状态"
              clearable
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="公开" :value="true" />
              <el-option label="私有" :value="false" />
            </el-select>
          </el-form-item>

          <el-form-item label="上传者">
            <el-select
              v-model="filterForm.uploaderId"
              placeholder="选择上传者"
              clearable
              filterable
              @change="handleFilter"
              style="width: 140px"
            >
              <el-option
                v-for="user in uploaderList"
                :key="user.id"
                :label="user.username"
                :value="user.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilter"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item label="排序">
            <el-select
              v-model="filterForm.sortBy"
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="上传时间" value="createdAt" />
              <el-option label="文件名称" value="originalName" />
              <el-option label="文件大小" value="size" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
              v-model="filterForm.sortOrder"
              @change="handleFilter"
              style="width: 80px"
            >
              <el-option label="降序" value="DESC" />
              <el-option label="升序" value="ASC" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="handleResetFilter">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 视图切换和批量选择信息 -->
        <div class="view-controls">
          <div class="view-toggle">
            <el-radio-group v-model="viewMode" @change="handleViewModeChange">
              <el-radio-button label="grid">
                <el-icon><Grid /></el-icon>
                网格视图
              </el-radio-button>
              <el-radio-button label="list">
                <el-icon><List /></el-icon>
                列表视图
              </el-radio-button>
            </el-radio-group>
          </div>

          <div v-if="selectedMedia.length > 0" class="batch-info">
            <span class="selected-count">已选择 {{ selectedMedia.length }} 个文件</span>
            <el-button
              type="text"
              @click="clearSelection"
              class="clear-selection"
            >
              清空选择
            </el-button>
          </div>

          <!-- 性能监控面板 -->
          <div class="performance-panel" v-if="performanceMetrics.renderTime > 0">
            <el-tooltip content="页面性能指标" placement="top">
              <div class="performance-info">
                <el-icon><Timer /></el-icon>
                <span>{{ performanceMetrics.renderTime.toFixed(1) }}ms</span>
                <span v-if="virtualScrollEnabled" class="virtual-scroll-indicator">
                  <el-icon><Lightning /></el-icon>
                  虚拟滚动
                </span>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 媒体展示区域 -->
    <el-card class="media-display-card" shadow="never">
      <div v-loading="loading" class="media-container" ref="containerRef">
        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="media-grid">
          <div
            v-for="(media, index) in mediaList"
            :key="media.id"
            class="media-card"
            :class="{
              selected: isSelected(media),
              dragging: isDragging && dragStartIndex === index,
              'drag-over': isDragging && dragOverIndex === index
            }"
            @click="handleMediaClick(media, $event)"
            @contextmenu="handleMediaContextMenu(media, $event)"
            draggable="true"
            @dragstart="handleDragStart($event, media, index)"
            @dragover="handleDragOver($event, index)"
            @drop="handleDrop($event, index)"
            @dragend="handleDragEnd"
          >
            <div class="media-card-content">
              <!-- 选择框 -->
              <div class="media-checkbox">
                <el-checkbox
                  :model-value="isSelected(media)"
                  @change="handleMediaSelect(media, $event)"
                  @click.stop
                />
              </div>

              <!-- 媒体预览 -->
              <div class="media-preview">
                <img
                  v-if="media.category === 'image'"
                  :src="media.thumbnailUrl || media.url"
                  :alt="media.originalName"
                  class="media-thumbnail"
                  @error="handleImageError"
                />
                <div v-else class="media-icon">
                  <el-icon v-if="media.category === 'video'"><VideoPlay /></el-icon>
                  <el-icon v-else-if="media.category === 'audio'"><Headset /></el-icon>
                  <el-icon v-else><Document /></el-icon>
                </div>

                <!-- 文件类型标签 -->
                <div class="media-type-tag">
                  <el-tag :type="getMediaTypeColor(media.category)" size="small">
                    {{ getMediaTypeLabel(media.category) }}
                  </el-tag>
                </div>

                <!-- 公开状态标签 -->
                <div class="media-status-tag">
                  <el-tag :type="media.isPublic ? 'success' : 'warning'" size="small">
                    {{ media.isPublic ? '公开' : '私有' }}
                  </el-tag>
                </div>
              </div>

              <!-- 媒体信息 -->
              <div class="media-info">
                <div class="media-name" :title="media.originalName">
                  {{ media.originalName }}
                </div>
                <div class="media-meta">
                  <span class="media-uploader">
                    <el-icon><User /></el-icon>
                    {{ media.uploader?.username || '未知' }}
                  </span>
                  <span class="media-date">
                    <el-icon><Calendar /></el-icon>
                    {{ formatDate(media.createdAt) }}
                  </span>
                </div>
                <div class="media-size">
                  <el-icon><Coin /></el-icon>
                  {{ formatFileSize(media.size) }}
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="media-actions">
                <el-button
                  type="text"
                  size="small"
                  :icon="View"
                  @click.stop="handlePreviewMedia(media)"
                  title="预览"
                />
                <el-button
                  type="text"
                  size="small"
                  :icon="Edit"
                  @click.stop="handleEditMedia(media)"
                  v-permission="'media.update'"
                  title="编辑"
                />
                <el-button
                  type="text"
                  size="small"
                  :icon="Download"
                  @click.stop="handleDownloadMedia(media)"
                  title="下载"
                />
                <el-button
                  type="text"
                  size="small"
                  :icon="Delete"
                  @click.stop="handleDeleteMedia(media)"
                  v-permission="'media.delete'"
                  class="danger-button"
                  title="删除"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-else class="media-list">
          <el-table
            :data="mediaList"
            @selection-change="handleSelectionChange"
            stripe
            class="media-table"
          >
            <el-table-column
              type="selection"
              width="55"
              v-if="hasPermission('media.delete')"
            />

            <el-table-column label="预览" width="80">
              <template #default="{ row }">
                <div class="table-thumbnail">
                  <img
                    v-if="row.category === 'image'"
                    :src="row.thumbnailUrl || row.url"
                    :alt="row.originalName"
                    class="thumbnail-image"
                    @error="handleImageError"
                  />
                  <div v-else class="thumbnail-icon">
                    <el-icon v-if="row.category === 'video'"><VideoPlay /></el-icon>
                    <el-icon v-else-if="row.category === 'audio'"><Headset /></el-icon>
                    <el-icon v-else><Document /></el-icon>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="文件信息" min-width="200">
              <template #default="{ row }">
                <div class="file-info">
                  <div class="file-name">{{ row.originalName }}</div>
                  <div class="file-meta">
                    <el-tag :type="getMediaTypeColor(row.category)" size="small">
                      {{ getMediaTypeLabel(row.category) }}
                    </el-tag>
                    <span class="file-size">{{ formatFileSize(row.size) }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="上传者" width="120">
              <template #default="{ row }">
                {{ row.uploader?.username || '未知' }}
              </template>
            </el-table-column>

            <el-table-column label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.isPublic ? 'success' : 'warning'" size="small">
                  {{ row.isPublic ? '公开' : '私有' }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column label="上传时间" width="160">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="table-actions">
                  <el-button
                    type="text"
                    size="small"
                    :icon="View"
                    @click="handlePreviewMedia(row)"
                  >
                    预览
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    :icon="Edit"
                    @click="handleEditMedia(row)"
                    v-permission="'media.update'"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    :icon="Download"
                    @click="handleDownloadMedia(row)"
                  >
                    下载
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    :icon="Delete"
                    @click="handleDeleteMedia(row)"
                    v-permission="'media.delete'"
                    class="danger-button"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && mediaList.length === 0" class="empty-state">
          <el-empty description="暂无媒体文件">
            <el-button
              type="primary"
              :icon="Upload"
              @click="handleUploadFiles"
              v-permission="'media.upload'"
            >
              上传第一个文件
            </el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[12, 24, 48, 96]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 媒体预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="currentMedia?.originalName"
      width="80%"
      :close-on-click-modal="false"
      class="media-preview-dialog"
    >
      <div v-if="currentMedia" class="preview-content">
        <!-- 图片预览 -->
        <div v-if="currentMedia.category === 'image'" class="image-preview">
          <img
            :src="currentMedia.url"
            :alt="currentMedia.originalName"
            class="preview-image"
            @load="handleImageLoad"
            @error="handleImageError"
          />
        </div>

        <!-- 视频预览 -->
        <div v-else-if="currentMedia.category === 'video'" class="video-preview">
          <video
            :src="currentMedia.url"
            controls
            class="preview-video"
            preload="metadata"
          >
            您的浏览器不支持视频播放
          </video>
        </div>

        <!-- 音频预览 -->
        <div v-else-if="currentMedia.category === 'audio'" class="audio-preview">
          <div class="audio-cover">
            <el-icon class="audio-icon"><Headset /></el-icon>
            <div class="audio-info">
              <div class="audio-title">{{ currentMedia.originalName }}</div>
              <div class="audio-size">{{ formatFileSize(currentMedia.size) }}</div>
            </div>
          </div>
          <audio
            :src="currentMedia.url"
            controls
            class="preview-audio"
            preload="metadata"
          >
            您的浏览器不支持音频播放
          </audio>
        </div>

        <!-- 文档预览 -->
        <div v-else class="document-preview">
          <div class="document-info">
            <el-icon class="document-icon"><Document /></el-icon>
            <div class="document-details">
              <div class="document-title">{{ currentMedia.originalName }}</div>
              <div class="document-meta">
                <span>文件大小: {{ formatFileSize(currentMedia.size) }}</span>
                <span>文件类型: {{ currentMedia.mimeType }}</span>
              </div>
            </div>
          </div>
          <div class="document-actions">
            <el-button type="primary" :icon="Download" @click="handleDownloadMedia(currentMedia)">
              下载文件
            </el-button>
          </div>
        </div>

        <!-- 媒体详细信息 -->
        <div class="media-details">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="文件名">
              {{ currentMedia.originalName }}
            </el-descriptions-item>
            <el-descriptions-item label="文件类型">
              <el-tag :type="getMediaTypeColor(currentMedia.category)">
                {{ getMediaTypeLabel(currentMedia.category) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(currentMedia.size) }}
            </el-descriptions-item>
            <el-descriptions-item label="MIME类型">
              {{ currentMedia.mimeType }}
            </el-descriptions-item>
            <el-descriptions-item label="上传者">
              {{ currentMedia.uploader?.username || '未知' }}
            </el-descriptions-item>
            <el-descriptions-item label="公开状态">
              <el-tag :type="currentMedia.isPublic ? 'success' : 'warning'">
                {{ currentMedia.isPublic ? '公开' : '私有' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="上传时间">
              {{ formatDate(currentMedia.createdAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间">
              {{ formatDate(currentMedia.updatedAt) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件URL" :span="2">
              <el-input :model-value="currentMedia.url" readonly>
                <template #append>
                  <el-button @click="copyToClipboard(currentMedia.url)">复制</el-button>
                </template>
              </el-input>
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ currentMedia.description || '暂无描述' }}
            </el-descriptions-item>
            <el-descriptions-item label="标签" :span="2">
              <div v-if="currentMedia.tags && currentMedia.tags.length > 0" class="tags-list">
                <el-tag
                  v-for="tag in currentMedia.tags"
                  :key="tag"
                  class="tag-item"
                  type="info"
                >
                  {{ tag }}
                </el-tag>
              </div>
              <span v-else class="no-tags">暂无标签</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button
            type="primary"
            :icon="Edit"
            @click="handleEditFromPreview"
            v-permission="'media.update'"
          >
            编辑信息
          </el-button>
          <el-button
            :icon="Download"
            @click="handleDownloadMedia(currentMedia!)"
          >
            下载文件
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 媒体编辑对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      :title="isEditMode ? '编辑媒体信息' : '媒体信息'"
      width="600px"
      :close-on-click-modal="false"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editFormRules"
        label-width="80px"
        @submit.prevent
      >
        <el-form-item label="文件名" prop="originalName">
          <el-input
            v-model="editForm.originalName"
            placeholder="请输入文件名"
            maxlength="100"
            show-word-limit
            readonly
          />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            placeholder="请输入文件描述"
            :rows="3"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="editForm.tags"
            multiple
            filterable
            allow-create
            placeholder="请选择或输入标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in commonTags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="公开状态" prop="isPublic">
          <el-switch
            v-model="editForm.isPublic"
            active-text="公开"
            inactive-text="私有"
          />
          <div class="form-tip">
            公开文件可以被所有用户访问，私有文件仅上传者可见
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="editSubmitLoading"
            @click="handleSubmitEdit"
          >
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传文件"
      width="700px"
      :close-on-click-modal="false"
      @close="handleUploadDialogClose"
    >
      <div class="upload-content">
        <!-- 拖拽上传区域 -->
        <el-upload
          ref="uploadRef"
          class="upload-dragger"
          drag
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :multiple="true"
          :auto-upload="false"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          :on-progress="handleUploadProgress"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="uploadFileList"
          :limit="maxUploadFiles"
          :accept="acceptedFileTypes"
        >
          <el-icon class="el-icon--upload"><Upload /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持图片、视频、音频、文档等格式，单个文件不超过{{ formatFileSize(maxFileSize) }}，最多{{ maxUploadFiles }}个文件
            </div>
          </template>
        </el-upload>

        <!-- 上传队列 -->
        <div v-if="uploadFileList.length > 0" class="upload-queue">
          <h4>上传队列 ({{ uploadFileList.length }}/{{ maxUploadFiles }})</h4>
          <div class="upload-list">
            <div
              v-for="file in uploadFileList"
              :key="file.uid"
              class="upload-item"
            >
              <div class="upload-item-info">
                <el-icon class="file-icon">
                  <Picture v-if="file.raw?.type.startsWith('image/')" />
                  <VideoPlay v-else-if="file.raw?.type.startsWith('video/')" />
                  <Headset v-else-if="file.raw?.type.startsWith('audio/')" />
                  <Document v-else />
                </el-icon>
                <div class="file-details">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-size">{{ formatFileSize(file.size || 0) }}</div>
                </div>
              </div>
              <div class="upload-item-status">
                <el-progress
                  v-if="file.status === 'uploading'"
                  :percentage="file.percentage || 0"
                  :stroke-width="6"
                />
                <el-tag v-else-if="file.status === 'success'" type="success" size="small">
                  上传成功
                </el-tag>
                <el-tag v-else-if="file.status === 'fail'" type="danger" size="small">
                  上传失败
                </el-tag>
                <el-tag v-else type="info" size="small">
                  等待上传
                </el-tag>
              </div>
              <div class="upload-item-actions">
                <el-button
                  type="text"
                  size="small"
                  :icon="Delete"
                  @click="handleRemoveUploadFile(file)"
                  :disabled="file.status === 'uploading'"
                >
                  移除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button @click="handleClearUploadList" :disabled="uploadFileList.length === 0">
            清空列表
          </el-button>
          <el-button
            type="primary"
            :loading="uploadLoading"
            @click="handleStartUpload"
            :disabled="uploadFileList.length === 0"
          >
            开始上传 ({{ uploadFileList.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
/**
 * 媒体管理页面
 *
 * 功能特性：
 * - 媒体文件展示和管理（图片、视频、音频、文档）
 * - 文件上传（拖拽上传、批量上传）
 * - 媒体预览和编辑
 * - 批量操作（删除、下载、状态修改）
 * - 筛选搜索和排序
 * - 网格视图和列表视图切换
 * - 响应式设计
 * - 权限控制
 */

import { ref, reactive, onMounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Picture,
  Upload,
  Download,
  Operation,
  ArrowDown,
  Files,
  VideoPlay,
  Headset, // 修正图标名称
  Document,
  Coin,
  Search,
  Grid,
  List,
  View,
  Edit,
  Delete,
  User,
  Calendar,
  Timer,
  Lightning
} from '@element-plus/icons-vue'
import { usePermission } from '@/composables/usePermission'
import { useMediaStore } from '@/stores/media'
import { MediaService } from '@/services/media'
import { formatDate } from '@/utils/date'
import type { Media, MediaCategory, MediaListQuery } from '@/types/media'
import type { User as UserType } from '@/types/user'

// ==================== 高级交互功能 ====================
// 右键菜单状态
const contextMenuVisible = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })
const contextMenuTarget = ref<Media | null>(null)

// 快捷键状态
const isCtrlPressed = ref(false)
const isShiftPressed = ref(false)

// 拖拽状态
const isDragging = ref(false)
const dragStartIndex = ref(-1)
const dragOverIndex = ref(-1)

// ==================== 性能优化相关 ====================
// 虚拟滚动状态
const virtualScrollEnabled = ref(false)
const containerRef = ref<HTMLElement>()
const itemHeight = 320 // 网格模式下每个卡片的高度
const visibleRange = reactive({ start: 0, end: 24 })

// 图片懒加载
const imageObserver = ref<IntersectionObserver | null>(null)
const loadedImages = new Set<string>()

// 性能监控
const performanceMetrics = reactive({
  renderTime: 0,
  loadTime: 0,
  memoryUsage: 0
})

// ==================== 权限检查 ====================
const { hasPermission } = usePermission()

// ==================== 状态管理 ====================
const mediaStore = useMediaStore()

// ==================== 基础数据 ====================
const loading = ref(false)
const viewMode = ref<'grid' | 'list'>('grid')
const selectedMedia = ref<Media[]>([])
const uploaderList = ref<UserType[]>([])

// ==================== 对话框状态 ====================
const previewDialogVisible = ref(false)
const editDialogVisible = ref(false)
const uploadDialogVisible = ref(false)
const isEditMode = ref(false)
const editSubmitLoading = ref(false)
const uploadLoading = ref(false)
const currentMedia = ref<Media | null>(null)

// ==================== 编辑表单 ====================
const editFormRef = ref()
const editForm = reactive({
  originalName: '',
  description: '',
  tags: [] as string[],
  isPublic: true
})

const editFormRules = {
  description: [
    { max: 500, message: '描述不能超过 500 个字符', trigger: 'blur' }
  ]
}

// 常用标签列表
const commonTags = ref([
  '重要', '工作', '个人', '项目', '设计', '开发', '测试', '文档', '图片', '视频'
])

// ==================== 上传相关 ====================
const uploadRef = ref()
const uploadFileList = ref<any[]>([])
const uploadAction = '/api/upload/image' // 这里应该根据文件类型动态设置
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
}
const uploadData = {}
const maxUploadFiles = 10
const maxFileSize = 50 * 1024 * 1024 // 50MB
const acceptedFileTypes = 'image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar'

// ==================== 分页配置 ====================
const pagination = reactive({
  page: 1,
  limit: 24,
  total: 0
})

// ==================== 筛选表单 ====================
const filterForm = reactive({
  search: '',
  category: undefined as MediaCategory | undefined,
  isPublic: undefined as boolean | undefined,
  uploaderId: undefined as number | undefined,
  dateRange: null as [Date, Date] | null,
  sortBy: 'createdAt' as 'createdAt' | 'size' | 'originalName',
  sortOrder: 'DESC' as 'ASC' | 'DESC'
})

// ==================== 计算属性 ====================
const mediaList = computed(() => mediaStore.mediaList)
const mediaStats = computed(() => mediaStore.stats)

// ==================== 工具方法 ====================
/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 获取媒体类型标签颜色
 */
const getMediaTypeColor = (category: MediaCategory): string => {
  const colorMap = {
    image: 'success',
    video: 'primary',
    audio: 'warning',
    document: 'info'
  }
  return colorMap[category] || 'info'
}

/**
 * 获取媒体类型标签文本
 */
const getMediaTypeLabel = (category: MediaCategory): string => {
  const labelMap = {
    image: '图片',
    video: '视频',
    audio: '音频',
    document: '文档'
  }
  return labelMap[category] || '未知'
}

/**
 * 检查媒体是否被选中
 */
const isSelected = (media: Media): boolean => {
  return selectedMedia.value.some(item => item.id === media.id)
}

/**
 * 处理图片加载错误
 */
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/images/file-placeholder.png' // 默认占位图
}

// ==================== 性能优化方法 ====================
/**
 * 初始化图片懒加载
 */
const initImageLazyLoading = () => {
  if (!window.IntersectionObserver) {
    return // 不支持IntersectionObserver的浏览器降级处理
  }

  imageObserver.value = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          const src = img.dataset.src
          if (src && !loadedImages.has(src)) {
            img.src = src
            loadedImages.add(src)
            imageObserver.value?.unobserve(img)
          }
        }
      })
    },
    {
      rootMargin: '50px', // 提前50px开始加载
      threshold: 0.1
    }
  )
}

/**
 * 观察图片元素
 */
const observeImage = (img: HTMLImageElement) => {
  if (imageObserver.value && img.dataset.src) {
    imageObserver.value.observe(img)
  }
}

/**
 * 计算虚拟滚动范围
 */
const calculateVisibleRange = () => {
  if (!containerRef.value || !virtualScrollEnabled.value) return

  const container = containerRef.value
  const scrollTop = container.scrollTop
  const containerHeight = container.clientHeight
  const itemsPerRow = Math.floor(container.clientWidth / 300) // 假设每个卡片宽度300px

  const startRow = Math.floor(scrollTop / itemHeight)
  const endRow = Math.ceil((scrollTop + containerHeight) / itemHeight)

  visibleRange.start = Math.max(0, startRow * itemsPerRow - itemsPerRow) // 预加载一行
  visibleRange.end = Math.min(mediaList.value.length, (endRow + 1) * itemsPerRow) // 预加载一行
}

/**
 * 虚拟滚动处理
 */
const handleVirtualScroll = () => {
  if (virtualScrollEnabled.value) {
    calculateVisibleRange()
  }
}

/**
 * 启用/禁用虚拟滚动
 */
const toggleVirtualScroll = (enabled: boolean) => {
  virtualScrollEnabled.value = enabled
  if (enabled) {
    calculateVisibleRange()
  } else {
    visibleRange.start = 0
    visibleRange.end = mediaList.value.length
  }
}

/**
 * 性能监控
 */
const measurePerformance = (operation: string, fn: () => void | Promise<void>) => {
  const startTime = performance.now()
  const startMemory = (performance as any).memory?.usedJSHeapSize || 0

  const result = fn()

  if (result instanceof Promise) {
    return result.then(() => {
      const endTime = performance.now()
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0

      performanceMetrics.renderTime = endTime - startTime
      performanceMetrics.memoryUsage = endMemory - startMemory

      console.log(`${operation} 性能指标:`, {
        时间: `${(endTime - startTime).toFixed(2)}ms`,
        内存: `${((endMemory - startMemory) / 1024 / 1024).toFixed(2)}MB`
      })
    })
  } else {
    const endTime = performance.now()
    const endMemory = (performance as any).memory?.usedJSHeapSize || 0

    performanceMetrics.renderTime = endTime - startTime
    performanceMetrics.memoryUsage = endMemory - startMemory

    console.log(`${operation} 性能指标:`, {
      时间: `${(endTime - startTime).toFixed(2)}ms`,
      内存: `${((endMemory - startMemory) / 1024 / 1024).toFixed(2)}MB`
    })
  }
}

/**
 * 防抖函数
 */
const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 */
const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle = false
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 图片缓存和预加载
const imageCache = new Map<string, HTMLImageElement>()
const preloadQueue = new Set<string>()

/**
 * 预加载图片
 */
const preloadImage = (url: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (imageCache.has(url)) {
      resolve()
      return
    }

    if (preloadQueue.has(url)) {
      resolve()
      return
    }

    preloadQueue.add(url)
    const img = new Image()

    img.onload = () => {
      imageCache.set(url, img)
      preloadQueue.delete(url)
      resolve()
    }

    img.onerror = () => {
      preloadQueue.delete(url)
      reject(new Error(`Failed to load image: ${url}`))
    }

    img.src = url
  })
}

/**
 * 批量预加载图片
 */
const batchPreloadImages = async (urls: string[]) => {
  const promises = urls.slice(0, 10).map(url => // 限制同时预加载的数量
    preloadImage(url).catch(error => console.warn('预加载失败:', error))
  )

  await Promise.allSettled(promises)
}

/**
 * 清理图片缓存
 */
const clearImageCache = () => {
  imageCache.clear()
  preloadQueue.clear()
}

// ==================== 数据加载 ====================
/**
 * 加载媒体列表
 */
const loadMediaList = async () => {
  try {
    loading.value = true

    const query: MediaListQuery = {
      page: pagination.page,
      limit: pagination.limit,
      search: filterForm.search || undefined,
      category: filterForm.category,
      isPublic: filterForm.isPublic,
      uploaderId: filterForm.uploaderId,
      sortBy: filterForm.sortBy,
      sortOrder: filterForm.sortOrder
    }

    // 处理日期范围
    if (filterForm.dateRange) {
      query.startDate = filterForm.dateRange[0].toISOString()
      query.endDate = filterForm.dateRange[1].toISOString()
    }

    await mediaStore.fetchMediaList(query)
    pagination.total = mediaStore.pagination.total

    // 预加载当前页面的图片
    const imageUrls = mediaList.value
      .filter(media => media.category === 'image')
      .map(media => media.thumbnailUrl || media.url)
      .filter(Boolean) as string[]

    if (imageUrls.length > 0) {
      batchPreloadImages(imageUrls)
    }
  } catch (error) {
    console.error('加载媒体列表失败:', error)
    ElMessage.error('加载媒体列表失败')
  } finally {
    loading.value = false
  }
}

// 创建防抖和节流版本的方法
const debouncedSearch = debounce(loadMediaList, 300)
const throttledScroll = throttle(handleVirtualScroll, 16) // 60fps

/**
 * 加载统计信息
 */
const loadMediaStats = async () => {
  try {
    // 使用MediaService直接获取统计信息
    const stats = await MediaService.getMediaStats()
    // 手动更新store中的统计信息
    mediaStore.stats = stats
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

/**
 * 加载上传者列表
 */
const loadUploaderList = async () => {
  try {
    // 这里应该调用用户服务获取用户列表
    // const users = await UserService.getUsers({ role: 'uploader' })
    // uploaderList.value = users.items || []
    uploaderList.value = [] // 临时空数组
  } catch (error) {
    console.error('加载上传者列表失败:', error)
  }
}

// ==================== 事件处理 ====================
/**
 * 防抖搜索
 */
const handleSearch = () => {
  pagination.page = 1
  debouncedSearch()
}

/**
 * 筛选处理
 */
const handleFilter = () => {
  pagination.page = 1
  loadMediaList()
}

/**
 * 重置筛选
 */
const handleResetFilter = () => {
  Object.assign(filterForm, {
    search: '',
    category: undefined,
    isPublic: undefined,
    uploaderId: undefined,
    dateRange: null,
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  })
  pagination.page = 1
  loadMediaList()
}

/**
 * 视图模式切换
 */
const handleViewModeChange = (mode: 'grid' | 'list') => {
  viewMode.value = mode
  // 网格视图和列表视图使用不同的分页大小
  pagination.limit = mode === 'grid' ? 24 : 20
  pagination.page = 1
  loadMediaList()
}

/**
 * 分页处理
 */
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  loadMediaList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadMediaList()
}

// ==================== 媒体选择操作 ====================
/**
 * 媒体点击处理
 */
const handleMediaClick = (media: Media, event: MouseEvent) => {
  if (event.ctrlKey || event.metaKey) {
    // Ctrl/Cmd + 点击：切换选择状态
    handleMediaSelect(media, !isSelected(media))
  } else if (event.shiftKey && selectedMedia.value.length > 0) {
    // Shift + 点击：范围选择
    handleRangeSelect(media)
  } else {
    // 普通点击：预览媒体
    handlePreviewMedia(media)
  }
}

/**
 * 媒体右键菜单处理
 */
const handleMediaContextMenu = (media: Media, event: MouseEvent) => {
  event.preventDefault()
  contextMenuTarget.value = media
  contextMenuPosition.x = event.clientX
  contextMenuPosition.y = event.clientY
  contextMenuVisible.value = true

  // 如果右键的媒体没有被选中，则选中它
  if (!isSelected(media)) {
    selectedMedia.value = [media]
  }
}

/**
 * 关闭右键菜单
 */
const closeContextMenu = () => {
  contextMenuVisible.value = false
  contextMenuTarget.value = null
}

/**
 * 快捷键处理
 */
const handleKeyDown = (event: KeyboardEvent) => {
  isCtrlPressed.value = event.ctrlKey || event.metaKey
  isShiftPressed.value = event.shiftKey

  // 全选 Ctrl+A
  if ((event.ctrlKey || event.metaKey) && event.key === 'a') {
    event.preventDefault()
    selectedMedia.value = [...mediaList.value]
    ElMessage.info(`已选择 ${mediaList.value.length} 个文件`)
  }

  // 删除 Delete
  if (event.key === 'Delete' && selectedMedia.value.length > 0) {
    event.preventDefault()
    handleBatchDelete()
  }

  // 复制 Ctrl+C
  if ((event.ctrlKey || event.metaKey) && event.key === 'c' && selectedMedia.value.length > 0) {
    event.preventDefault()
    handleCopyMediaUrls()
  }

  // 刷新 F5
  if (event.key === 'F5') {
    event.preventDefault()
    loadMediaList()
    ElMessage.success('列表已刷新')
  }

  // 上传 Ctrl+U
  if ((event.ctrlKey || event.metaKey) && event.key === 'u') {
    event.preventDefault()
    handleUploadFiles()
  }
}

const handleKeyUp = (event: KeyboardEvent) => {
  isCtrlPressed.value = event.ctrlKey || event.metaKey
  isShiftPressed.value = event.shiftKey
}

/**
 * 复制媒体URL到剪贴板
 */
const handleCopyMediaUrls = async () => {
  try {
    const urls = selectedMedia.value.map(media => media.url).join('\n')
    await navigator.clipboard.writeText(urls)
    ElMessage.success(`已复制 ${selectedMedia.value.length} 个文件链接`)
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

/**
 * 拖拽开始
 */
const handleDragStart = (event: DragEvent, media: Media, index: number) => {
  if (!event.dataTransfer) return

  isDragging.value = true
  dragStartIndex.value = index

  // 设置拖拽数据
  event.dataTransfer.setData('text/plain', media.id.toString())
  event.dataTransfer.effectAllowed = 'move'

  // 如果拖拽的媒体没有被选中，则选中它
  if (!isSelected(media)) {
    selectedMedia.value = [media]
  }
}

/**
 * 拖拽经过
 */
const handleDragOver = (event: DragEvent, index: number) => {
  event.preventDefault()
  if (!isDragging.value) return

  dragOverIndex.value = index
  event.dataTransfer!.dropEffect = 'move'
}

/**
 * 拖拽放置
 */
const handleDrop = (event: DragEvent, targetIndex: number) => {
  event.preventDefault()

  if (!isDragging.value || dragStartIndex.value === -1) return

  const startIndex = dragStartIndex.value

  if (startIndex !== targetIndex) {
    // 这里可以实现拖拽排序的逻辑
    // 由于媒体列表通常按时间排序，这里主要是演示拖拽功能
    ElMessage.info(`拖拽功能演示：从位置 ${startIndex + 1} 移动到位置 ${targetIndex + 1}`)
  }

  // 重置拖拽状态
  isDragging.value = false
  dragStartIndex.value = -1
  dragOverIndex.value = -1
}

/**
 * 拖拽结束
 */
const handleDragEnd = () => {
  isDragging.value = false
  dragStartIndex.value = -1
  dragOverIndex.value = -1
}

/**
 * 媒体选择处理
 */
const handleMediaSelect = (media: Media, selected: boolean) => {
  if (selected) {
    if (!isSelected(media)) {
      selectedMedia.value.push(media)
    }
  } else {
    const index = selectedMedia.value.findIndex(item => item.id === media.id)
    if (index > -1) {
      selectedMedia.value.splice(index, 1)
    }
  }
}

/**
 * 范围选择处理
 */
const handleRangeSelect = (endMedia: Media) => {
  const startIndex = mediaList.value.findIndex(item =>
    item.id === selectedMedia.value[selectedMedia.value.length - 1]?.id
  )
  const endIndex = mediaList.value.findIndex(item => item.id === endMedia.id)

  if (startIndex !== -1 && endIndex !== -1) {
    const start = Math.min(startIndex, endIndex)
    const end = Math.max(startIndex, endIndex)

    for (let i = start; i <= end; i++) {
      const media = mediaList.value[i]
      if (!isSelected(media)) {
        selectedMedia.value.push(media)
      }
    }
  }
}

/**
 * 表格选择变化处理
 */
const handleSelectionChange = (selection: Media[]) => {
  selectedMedia.value = selection
}

/**
 * 清空选择
 */
const clearSelection = () => {
  selectedMedia.value = []
}

// ==================== 媒体操作 ====================
/**
 * 预览媒体
 */
const handlePreviewMedia = async (media: Media) => {
  try {
    // 获取媒体详细信息
    const mediaDetail = await MediaService.getMedia(media.id)
    currentMedia.value = mediaDetail
    previewDialogVisible.value = true
  } catch (error) {
    console.error('获取媒体详情失败:', error)
    ElMessage.error('获取媒体详情失败')
  }
}

/**
 * 编辑媒体
 */
const handleEditMedia = async (media: Media) => {
  try {
    // 获取媒体详细信息
    const mediaDetail = await MediaService.getMedia(media.id)
    currentMedia.value = mediaDetail

    // 填充编辑表单
    Object.assign(editForm, {
      originalName: mediaDetail.originalName,
      description: mediaDetail.description || '',
      tags: mediaDetail.tags || [],
      isPublic: mediaDetail.isPublic
    })

    isEditMode.value = true
    editDialogVisible.value = true
  } catch (error) {
    console.error('获取媒体详情失败:', error)
    ElMessage.error('获取媒体详情失败')
  }
}

/**
 * 从预览对话框进入编辑
 */
const handleEditFromPreview = () => {
  if (currentMedia.value) {
    previewDialogVisible.value = false
    handleEditMedia(currentMedia.value)
  }
}

/**
 * 处理图片加载
 */
const handleImageLoad = (event: Event) => {
  const img = event.target as HTMLImageElement
  console.log('图片加载成功:', img.src)
}

/**
 * 复制到剪贴板
 */
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

/**
 * 编辑对话框关闭处理
 */
const handleEditDialogClose = () => {
  resetEditForm()
}

/**
 * 重置编辑表单
 */
const resetEditForm = () => {
  Object.assign(editForm, {
    originalName: '',
    description: '',
    tags: [],
    isPublic: true
  })
  editFormRef.value?.clearValidate()
}

/**
 * 提交编辑
 */
const handleSubmitEdit = async () => {
  if (!currentMedia.value) return

  try {
    await editFormRef.value?.validate()
    editSubmitLoading.value = true

    const updateData = {
      description: editForm.description || undefined,
      tags: editForm.tags.length > 0 ? editForm.tags : undefined,
      isPublic: editForm.isPublic
    }

    await MediaService.updateMedia(currentMedia.value.id, updateData)
    ElMessage.success('媒体信息更新成功')

    editDialogVisible.value = false
    loadMediaList() // 重新加载列表
  } catch (error) {
    console.error('更新媒体信息失败:', error)
    ElMessage.error('更新媒体信息失败')
  } finally {
    editSubmitLoading.value = false
  }
}

/**
 * 下载媒体
 */
const handleDownloadMedia = async (media: Media) => {
  try {
    MediaService.downloadMedia(media)
    ElMessage.success('下载开始')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}

/**
 * 删除媒体
 */
const handleDeleteMedia = async (media: Media) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${media.originalName}" 吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await MediaService.deleteMedia(media.id)
    ElMessage.success('删除成功')

    // 从选中列表中移除
    const index = selectedMedia.value.findIndex(item => item.id === media.id)
    if (index > -1) {
      selectedMedia.value.splice(index, 1)
    }

    // 重新加载列表
    loadMediaList()
    loadMediaStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// ==================== 批量操作 ====================
/**
 * 批量操作处理
 */
const handleBatchOperation = async (command: string) => {
  if (selectedMedia.value.length === 0) {
    ElMessage.warning('请先选择要操作的文件')
    return
  }

  switch (command) {
    case 'download':
      await handleBatchDownload()
      break
    case 'public':
      await handleBatchUpdateStatus(true)
      break
    case 'private':
      await handleBatchUpdateStatus(false)
      break
    case 'delete':
      await handleBatchDelete()
      break
  }
}

/**
 * 批量下载
 */
const handleBatchDownload = async () => {
  try {
    for (const media of selectedMedia.value) {
      MediaService.downloadMedia(media)
    }
    ElMessage.success('批量下载开始')
  } catch (error) {
    console.error('批量下载失败:', error)
    ElMessage.error('批量下载失败')
  }
}

/**
 * 批量更新状态
 */
const handleBatchUpdateStatus = async (isPublic: boolean) => {
  try {
    await ElMessageBox.confirm(
      `确定要将选中的 ${selectedMedia.value.length} 个文件设为${isPublic ? '公开' : '私有'}吗？`,
      '批量操作确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const mediaIds = selectedMedia.value.map(media => media.id)
    await MediaService.batchOperation({
      operation: 'updateStatus',
      mediaIds,
      data: { isPublic }
    })

    ElMessage.success('批量更新成功')
    clearSelection()
    loadMediaList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量更新失败:', error)
      ElMessage.error('批量更新失败')
    }
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedMedia.value.length} 个文件吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const mediaIds = selectedMedia.value.map(media => media.id)
    await MediaService.batchOperation({
      operation: 'delete',
      mediaIds
    })

    ElMessage.success('批量删除成功')
    clearSelection()
    loadMediaList()
    loadMediaStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// ==================== 文件上传 ====================
/**
 * 上传文件处理
 */
const handleUploadFiles = () => {
  uploadDialogVisible.value = true
}

/**
 * 文件变化处理
 */
const handleFileChange = (_file: any, fileList: any[]) => {
  uploadFileList.value = fileList
}

/**
 * 移除文件处理
 */
const handleFileRemove = (_file: any, fileList: any[]) => {
  uploadFileList.value = fileList
}

/**
 * 上传前检查
 */
const beforeUpload = (file: File) => {
  // 检查文件大小
  if (file.size > maxFileSize) {
    ElMessage.error(`文件大小不能超过 ${formatFileSize(maxFileSize)}`)
    return false
  }

  // 检查文件类型
  const allowedTypes = [
    'image/jpeg', 'image/png', 'image/gif', 'image/webp',
    'video/mp4', 'video/avi', 'video/mov', 'video/wmv',
    'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a',
    'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain', 'application/zip', 'application/x-rar-compressed'
  ]

  if (!allowedTypes.includes(file.type)) {
    ElMessage.error('不支持的文件类型')
    return false
  }

  return true
}

/**
 * 上传进度处理
 */
const handleUploadProgress = (event: any, file: any) => {
  file.percentage = Math.round((event.loaded / event.total) * 100)
}

/**
 * 上传成功处理
 */
const handleUploadSuccess = (response: any, file: any) => {
  if (response.success) {
    file.status = 'success'
    ElMessage.success(`${file.name} 上传成功`)
  } else {
    file.status = 'fail'
    ElMessage.error(`${file.name} 上传失败: ${response.message}`)
  }
}

/**
 * 上传失败处理
 */
const handleUploadError = (error: any, file: any) => {
  file.status = 'fail'
  console.error('上传失败:', error)
  ElMessage.error(`${file.name} 上传失败`)
}

/**
 * 移除上传文件
 */
const handleRemoveUploadFile = (file: any) => {
  const index = uploadFileList.value.findIndex(item => item.uid === file.uid)
  if (index > -1) {
    uploadFileList.value.splice(index, 1)
  }
}

/**
 * 清空上传列表
 */
const handleClearUploadList = () => {
  uploadFileList.value = []
  uploadRef.value?.clearFiles()
}

/**
 * 开始上传
 */
const handleStartUpload = async () => {
  if (uploadFileList.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }

  try {
    uploadLoading.value = true

    // 使用Element Plus的上传组件进行上传
    uploadRef.value?.submit()

    // 等待所有文件上传完成
    await new Promise((resolve) => {
      const checkUploadStatus = () => {
        const allFinished = uploadFileList.value.every(file =>
          file.status === 'success' || file.status === 'fail'
        )

        if (allFinished) {
          resolve(true)
        } else {
          setTimeout(checkUploadStatus, 500)
        }
      }
      checkUploadStatus()
    })

    const successCount = uploadFileList.value.filter(file => file.status === 'success').length
    const failCount = uploadFileList.value.filter(file => file.status === 'fail').length

    if (successCount > 0) {
      ElMessage.success(`成功上传 ${successCount} 个文件${failCount > 0 ? `，${failCount} 个文件上传失败` : ''}`)

      // 重新加载媒体列表和统计信息
      loadMediaList()
      loadMediaStats()

      // 如果全部成功，关闭对话框
      if (failCount === 0) {
        uploadDialogVisible.value = false
        handleClearUploadList()
      }
    } else {
      ElMessage.error('所有文件上传失败')
    }
  } catch (error) {
    console.error('上传过程出错:', error)
    ElMessage.error('上传过程出错')
  } finally {
    uploadLoading.value = false
  }
}

/**
 * 上传对话框关闭处理
 */
const handleUploadDialogClose = () => {
  if (uploadLoading.value) {
    ElMessage.warning('文件正在上传中，请稍候')
    return false
  }
  handleClearUploadList()
}

// ==================== 导出功能 ====================
/**
 * 导出媒体数据
 */
const handleExportMedia = async () => {
  try {
    // 获取所有媒体数据
    const response = await MediaService.getMediaList({ limit: 1000 })
    const allMedia = response.items || []

    // 准备导出数据
    const exportData = allMedia.map(media => ({
      '文件名': media.originalName,
      '文件类型': getMediaTypeLabel(media.category),
      '文件大小': formatFileSize(media.size),
      '上传者': media.uploader?.username || '未知',
      '公开状态': media.isPublic ? '公开' : '私有',
      '上传时间': formatDate(media.createdAt),
      '文件URL': media.url,
      '描述': media.description || ''
    }))

    // 创建CSV内容
    const headers = Object.keys(exportData[0] || {})
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
      )
    ].join('\n')

    // 下载文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `媒体文件列表_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('媒体数据导出成功')
  } catch (error) {
    console.error('导出媒体数据失败:', error)
    ElMessage.error('导出媒体数据失败')
  }
}

// ==================== 生命周期 ====================
/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  // 性能监控包装的数据加载
  measurePerformance('初始化数据加载', async () => {
    await Promise.all([
      loadMediaList(),
      loadMediaStats(),
      loadUploaderList()
    ])
  })

  // 初始化性能优化功能
  initImageLazyLoading()

  // 根据数据量决定是否启用虚拟滚动
  watch(mediaList, (newList) => {
    if (newList.length > 100) {
      toggleVirtualScroll(true)
      ElMessage.info('数据量较大，已自动启用虚拟滚动优化')
    }
  }, { immediate: true })

  // 添加滚动事件监听（用于虚拟滚动）
  if (containerRef.value) {
    containerRef.value.addEventListener('scroll', throttledScroll)
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown)
  document.addEventListener('keyup', handleKeyUp)
  document.addEventListener('click', closeContextMenu)
})

/**
 * 组件卸载时清理事件监听
 */
import { onUnmounted } from 'vue'
onUnmounted(() => {
  // 清理键盘事件监听
  document.removeEventListener('keydown', handleKeyDown)
  document.removeEventListener('keyup', handleKeyUp)
  document.removeEventListener('click', closeContextMenu)

  // 清理滚动事件监听
  if (containerRef.value) {
    containerRef.value.removeEventListener('scroll', throttledScroll)
  }

  // 清理图片观察器
  if (imageObserver.value) {
    imageObserver.value.disconnect()
  }

  // 清理性能监控数据
  loadedImages.clear()
})

/**
 * 监听筛选条件变化
 */
watch(
  () => filterForm.category,
  () => {
    if (filterForm.category) {
      handleFilter()
    }
  }
)
</script>

<style scoped>
.media-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .el-icon {
  font-size: 32px;
}

.page-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.images {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.videos {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-icon.audios {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #666;
}

.stats-icon.documents {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #666;
}

.stats-icon.storage {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  color: #666;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

/* 筛选卡片样式 */
.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.filter-content {
  padding: 20px;
}

.filter-form {
  margin-bottom: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.view-toggle .el-radio-group {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 4px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
}

.selected-count {
  font-weight: 500;
}

.clear-selection {
  color: #409eff;
  padding: 0;
  font-size: 14px;
}

.performance-panel {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.performance-info {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.virtual-scroll-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  color: #67c23a;
  font-weight: 500;
}

/* 媒体展示卡片样式 */
.media-display-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.media-container {
  padding: 20px;
  min-height: 400px;
}

/* 网格视图样式 */
.media-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.media-card {
  border: 2px solid transparent;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.media-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.media-card.selected {
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
}

.media-card.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.media-card.drag-over {
  border-color: #67c23a;
  box-shadow: 0 4px 20px rgba(103, 194, 58, 0.3);
}

.media-card-content {
  position: relative;
}

.media-checkbox {
  position: absolute;
  top: 12px;
  left: 12px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 4px;
}

.media-preview {
  position: relative;
  height: 200px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.media-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.media-icon {
  font-size: 48px;
  color: #909399;
}

.media-type-tag {
  position: absolute;
  top: 12px;
  right: 12px;
}

.media-status-tag {
  position: absolute;
  bottom: 12px;
  right: 12px;
}

.media-info {
  padding: 16px;
}

.media-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.media-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.media-uploader,
.media-date {
  display: flex;
  align-items: center;
  gap: 4px;
}

.media-size {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.media-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-top: 1px solid #f0f2f5;
  background: #fafbfc;
}

.media-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.danger-button {
  color: #f56c6c !important;
}

.danger-button:hover {
  color: #f78989 !important;
  background-color: #fef0f0 !important;
}

/* 列表视图样式 */
.media-list {
  width: 100%;
}

.media-table {
  width: 100%;
}

.media-table .el-table__header {
  background-color: #fafbfc;
}

.media-table .el-table__header th {
  background-color: #fafbfc !important;
  color: #606266;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.table-thumbnail {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f7fa;
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-icon {
  font-size: 24px;
  color: #909399;
}

.file-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-name {
  font-weight: 600;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.table-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.table-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .media-management {
    padding: 16px;
  }

  .header-content {
    padding: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-title .el-icon {
    font-size: 28px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions .el-button {
    width: 100%;
    justify-content: center;
  }

  .stats-content {
    padding: 16px;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 12px;
  }

  .stats-value {
    font-size: 24px;
  }

  .filter-content {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .filter-form .el-input,
  .filter-form .el-select {
    width: 100% !important;
  }

  .view-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .media-container {
    padding: 16px;
  }

  .media-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }

  .media-card {
    border-radius: 8px;
  }

  .media-preview {
    height: 160px;
  }

  .media-info {
    padding: 12px;
  }

  .media-name {
    font-size: 14px;
  }

  .media-actions {
    padding: 8px 12px;
  }

  .table-thumbnail {
    width: 50px;
    height: 50px;
  }

  .pagination-container {
    padding: 16px 0;
  }

  .pagination-container :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .media-management {
    padding: 12px;
  }

  .header-content {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-title .el-icon {
    font-size: 24px;
  }

  .stats-content {
    padding: 12px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 20px;
  }

  .stats-label {
    font-size: 12px;
  }

  .filter-content,
  .media-container {
    padding: 12px;
  }

  .media-grid {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }

  .media-preview {
    height: 120px;
  }

  .media-info {
    padding: 8px;
  }

  .media-name {
    font-size: 13px;
  }

  .media-meta {
    font-size: 11px;
  }

  .media-actions {
    padding: 6px 8px;
    gap: 4px;
  }

  .media-actions .el-button {
    padding: 2px 4px;
    font-size: 11px;
  }
}

/* 下拉菜单危险项样式 */
:deep(.el-dropdown-menu__item.danger) {
  color: #f56c6c;
}

:deep(.el-dropdown-menu__item.danger:hover) {
  background-color: #fef0f0;
  color: #f56c6c;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 媒体预览对话框样式 */
.media-preview-dialog {
  max-width: 1200px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.image-preview {
  text-align: center;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 20px;
}

.preview-image {
  max-width: 100%;
  max-height: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.video-preview {
  text-align: center;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.preview-video {
  width: 100%;
  max-height: 500px;
}

.audio-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
}

.audio-cover {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.audio-icon {
  font-size: 48px;
  color: #409eff;
}

.audio-info {
  flex: 1;
}

.audio-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.audio-size {
  font-size: 14px;
  color: #909399;
}

.preview-audio {
  width: 100%;
}

.document-preview {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #f5f7fa;
  border-radius: 8px;
  text-align: center;
}

.document-info {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.document-icon {
  font-size: 48px;
  color: #909399;
}

.document-details {
  text-align: left;
}

.document-title {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.document-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 14px;
  color: #909399;
}

.document-actions {
  display: flex;
  justify-content: center;
}

.media-details {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  margin: 0;
}

.no-tags {
  color: #909399;
  font-style: italic;
}

/* 编辑对话框样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 上传对话框样式 */
.upload-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: #409eff;
  background: #f0f9ff;
}

.upload-dragger :deep(.el-icon--upload) {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 16px;
}

.upload-dragger :deep(.el-upload__text) {
  font-size: 16px;
  color: #606266;
}

.upload-dragger :deep(.el-upload__text em) {
  color: #409eff;
  font-style: normal;
}

.upload-dragger :deep(.el-upload__tip) {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
}

.upload-queue {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
}

.upload-queue h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.upload-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.upload-item-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 24px;
  color: #409eff;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.upload-item-status {
  flex-shrink: 0;
  min-width: 120px;
}

.upload-item-actions {
  flex-shrink: 0;
}
</style>