/**
 * 媒体相关的类型定义
 */

import type { User } from './user'

/**
 * 媒体类别类型
 */
export type MediaCategory = 'image' | 'video' | 'audio' | 'document'

/**
 * 媒体基础属性接口
 */
export interface Media {
    id: number
    filename: string
    originalName: string
    mimeType: string
    size: number
    url: string
    thumbnailUrl?: string
    width?: number
    height?: number
    uploaderId: number
    uploader?: User
    category: MediaCategory
    tags?: string[]
    description?: string
    isPublic: boolean
    createdAt: string
    updatedAt: string
}

/**
 * 媒体上传请求数据
 */
export interface MediaUploadRequest {
    description?: string
    tags?: string[]
    isPublic?: boolean
}

/**
 * 媒体更新请求数据
 */
export interface MediaUpdateRequest {
    description?: string
    tags?: string[]
    isPublic?: boolean
}

/**
 * 媒体列表查询参数
 */
export interface MediaListQuery {
    page?: number
    limit?: number
    category?: MediaCategory
    search?: string
    uploaderId?: number
    isPublic?: boolean
    sortBy?: 'createdAt' | 'size' | 'originalName'
    sortOrder?: 'ASC' | 'DESC'
    startDate?: string
    endDate?: string
}

/**
 * 分页信息
 */
export interface MediaPagination {
    currentPage: number
    totalPages: number
    totalItems: number
    total: number // 添加total属性以兼容现有代码
    itemsPerPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
}

/**
 * 媒体列表响应数据
 */
export interface MediaListResponse {
    items: Media[] // 添加items属性以兼容现有代码
    media: Media[]
    pagination: MediaPagination
}

/**
 * 媒体统计信息
 */
export interface MediaStats {
    totalFiles: number
    total: number
    imageCount: number
    videoCount: number
    audioCount: number
    documentCount: number
    byCategory: {
        image: number
        video: number
        audio: number
        document: number
    }
    totalSize: number
    formattedTotalSize: string
}

/**
 * 文件上传状态
 */
export interface FileUploadStatus {
    file: File
    id?: string
    progress: number
    status: 'pending' | 'uploading' | 'success' | 'error'
    url?: string
    error?: string
    mediaId?: number
}

/**
 * 上传配置
 */
export interface UploadConfig {
    maxFileSize: number
    maxFiles: number
    allowedMimeTypes: string[]
    supportedCategories: MediaCategory[]
}

/**
 * API响应基础结构
 */
export interface ApiResponse<T = any> {
    success: boolean
    message?: string
    data?: T
    error?: string
}

/**
 * 媒体表单数据
 */
export interface MediaFormData {
    description: string
    tags: string[]
    isPublic: boolean
}

/**
 * 媒体预览信息
 */
export interface MediaPreview {
    id: number
    url: string
    thumbnailUrl?: string
    type: MediaCategory
    name: string
    size: number
    mimeType: string
}

/**
 * 批量操作类型
 */
export type BatchOperation = 'delete' | 'makePublic' | 'makePrivate' | 'addTags' | 'removeTags' | 'updateStatus'

/**
 * 批量操作请求
 */
export interface BatchOperationRequest {
    operation: BatchOperation
    mediaIds: number[]
    data?: {
        tags?: string[]
        isPublic?: boolean
    }
}