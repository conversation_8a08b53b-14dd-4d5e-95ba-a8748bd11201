<template>
  <div class="comment-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><ChatDotRound /></el-icon>
            评论管理
          </h1>
          <p class="page-description">管理用户评论、审核内容和维护社区秩序</p>
        </div>
        <div class="header-actions">
          <el-button
            type="primary"
            :icon="Check"
            @click="handleBatchApprove"
            v-permission="'comment.moderate'"
            :disabled="selectedComments.length === 0"
          >
            批量审核
          </el-button>
          <el-button
            :icon="Download"
            @click="handleExportComments"
            v-permission="'comment.list'"
          >
            导出数据
          </el-button>
          <el-dropdown @command="handleBatchOperation" v-if="selectedComments.length > 0">
            <el-button :icon="Operation">
              批量操作 <el-icon><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="approve">批量通过</el-dropdown-item>
                <el-dropdown-item command="reject">批量拒绝</el-dropdown-item>
                <el-dropdown-item command="delete" class="danger">批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><ChatDotRound /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ commentStats?.totalComments || 0 }}</div>
                <div class="stats-label">总评论数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ commentStats?.pendingCount || 0 }}</div>
                <div class="stats-label">待审核</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon approved">
                <el-icon><CircleCheck /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ commentStats?.approvedCount || 0 }}</div>
                <div class="stats-label">已通过</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rejected">
                <el-icon><CircleClose /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ commentStats?.rejectedCount || 0 }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon today">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ commentStats?.todayCount || 0 }}</div>
                <div class="stats-label">今日评论</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="8" :lg="6" :xl="4">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon replies">
                <el-icon><ChatLineRound /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ commentStats?.replyCount || 0 }}</div>
                <div class="stats-label">回复数量</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选和搜索区域 -->
    <el-card class="filter-card" shadow="never">
      <div class="filter-content">
        <el-form :model="filterForm" inline class="filter-form">
          <el-form-item label="搜索">
            <el-input
              v-model="filterForm.search"
              placeholder="搜索评论内容或作者"
              :prefix-icon="Search"
              clearable
              @input="handleSearch"
              style="width: 280px"
            />
          </el-form-item>

          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="选择审核状态"
              clearable
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="待审核" value="pending" />
              <el-option label="已通过" value="approved" />
              <el-option label="已拒绝" value="rejected" />
            </el-select>
          </el-form-item>

          <el-form-item label="类型">
            <el-select
              v-model="filterForm.type"
              placeholder="选择评论类型"
              clearable
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="文章评论" value="article" />
              <el-option label="说说评论" value="post" />
            </el-select>
          </el-form-item>

          <el-form-item label="作者">
            <el-select
              v-model="filterForm.authorId"
              placeholder="选择评论作者"
              clearable
              filterable
              remote
              :remote-method="searchUsers"
              @change="handleFilter"
              style="width: 140px"
            >
              <el-option
                v-for="user in userList"
                :key="user.id"
                :label="user.username"
                :value="user.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="时间">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleFilter"
              style="width: 240px"
            />
          </el-form-item>

          <el-form-item label="排序">
            <el-select
              v-model="filterForm.sortBy"
              @change="handleFilter"
              style="width: 120px"
            >
              <el-option label="创建时间" value="createdAt" />
              <el-option label="更新时间" value="updatedAt" />
              <el-option label="作者名称" value="author" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-select
              v-model="filterForm.sortOrder"
              @change="handleFilter"
              style="width: 80px"
            >
              <el-option label="降序" value="DESC" />
              <el-option label="升序" value="ASC" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button @click="handleResetFilter">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 视图切换和批量选择信息 -->
        <div class="view-controls">
          <div class="view-toggle">
            <el-radio-group v-model="viewMode" @change="handleViewModeChange">
              <el-radio-button label="list">
                <el-icon><List /></el-icon>
                列表视图
              </el-radio-button>
              <el-radio-button label="tree">
                <el-icon><Share /></el-icon>
                树形视图
              </el-radio-button>
            </el-radio-group>
          </div>

          <div v-if="selectedComments.length > 0" class="batch-info">
            <span class="selected-count">已选择 {{ selectedComments.length }} 条评论</span>
            <el-button
              type="text"
              @click="clearSelection"
              class="clear-selection"
            >
              清空选择
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 评论展示区域 -->
    <el-card class="comment-display-card" shadow="never">
      <div v-loading="loading" class="comment-container">
        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="comment-list">
          <el-table
            :data="commentList"
            @selection-change="handleSelectionChange"
            stripe
            class="comment-table"
          >
            <el-table-column
              type="selection"
              width="55"
              v-if="hasPermission('comment.moderate')"
            />

            <el-table-column label="评论内容" min-width="300">
              <template #default="{ row }">
                <div class="comment-content">
                  <div class="content-text" v-html="row.content"></div>
                  <div class="content-meta">
                    <el-tag :type="getStatusColor(row.status)" size="small">
                      {{ getStatusLabel(row.status) }}
                    </el-tag>
                    <el-tag v-if="row.articleId" type="info" size="small">文章评论</el-tag>
                    <el-tag v-if="row.postId" type="warning" size="small">说说评论</el-tag>
                    <span v-if="row.parentId" class="reply-indicator">
                      <el-icon><Share /></el-icon>
                      回复
                    </span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="作者" width="120">
              <template #default="{ row }">
                <div class="author-info">
                  <div class="author-name">{{ row.author?.username || '未知用户' }}</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="关联内容" width="150">
              <template #default="{ row }">
                <div class="related-content">
                  <div v-if="row.article" class="article-link">
                    <el-link type="primary" @click="viewArticle(row.article.id)">
                      {{ row.article.title }}
                    </el-link>
                  </div>
                  <div v-if="row.post" class="post-link">
                    <el-link type="warning" @click="viewPost(row.post.id)">
                      说说内容
                    </el-link>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="创建时间" width="160">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <div class="table-actions">
                  <el-button
                    type="text"
                    size="small"
                    :icon="View"
                    @click="handleViewComment(row)"
                  >
                    查看
                  </el-button>
                  <el-button
                    v-if="row.status === 'pending'"
                    type="text"
                    size="small"
                    :icon="Check"
                    @click="handleApproveComment(row)"
                    v-permission="'comment.moderate'"
                  >
                    通过
                  </el-button>
                  <el-button
                    v-if="row.status === 'pending'"
                    type="text"
                    size="small"
                    :icon="Close"
                    @click="handleRejectComment(row)"
                    v-permission="'comment.moderate'"
                  >
                    拒绝
                  </el-button>
                  <el-button
                    type="text"
                    size="small"
                    :icon="Delete"
                    @click="handleDeleteComment(row)"
                    v-permission="'comment.delete'"
                    class="danger-button"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 树形视图 -->
        <div v-else class="comment-tree">
          <div
            v-for="comment in commentTree"
            :key="comment.id"
            class="comment-item"
            :class="{ selected: isSelected(comment) }"
          >
            <div class="comment-card">
              <div class="comment-header">
                <div class="comment-checkbox" v-if="hasPermission('comment.moderate')">
                  <el-checkbox
                    :model-value="isSelected(comment)"
                    @change="handleCommentSelect(comment, $event)"
                  />
                </div>
                <div class="comment-author">
                  <span class="author-name">{{ comment.author?.username || '未知用户' }}</span>
                  <el-tag :type="getStatusColor(comment.status)" size="small">
                    {{ getStatusLabel(comment.status) }}
                  </el-tag>
                </div>
                <div class="comment-time">{{ formatDate(comment.createdAt) }}</div>
              </div>

              <div class="comment-body">
                <div class="comment-text" v-html="comment.content"></div>
                <div class="comment-meta">
                  <el-tag v-if="comment.articleId" type="info" size="small">文章评论</el-tag>
                  <el-tag v-if="comment.postId" type="warning" size="small">说说评论</el-tag>
                  <div v-if="comment.article" class="related-article">
                    关联文章：
                    <el-link type="primary" @click="viewArticle(comment.article.id)">
                      {{ comment.article.title }}
                    </el-link>
                  </div>
                  <div v-if="comment.post" class="related-post">
                    关联说说：
                    <el-link type="warning" @click="viewPost(comment.post.id)">
                      查看说说
                    </el-link>
                  </div>
                </div>
              </div>

              <div class="comment-actions">
                <el-button
                  type="text"
                  size="small"
                  :icon="View"
                  @click="handleViewComment(comment)"
                >
                  查看
                </el-button>
                <el-button
                  v-if="comment.status === 'pending'"
                  type="text"
                  size="small"
                  :icon="Check"
                  @click="handleApproveComment(comment)"
                  v-permission="'comment.moderate'"
                >
                  通过
                </el-button>
                <el-button
                  v-if="comment.status === 'pending'"
                  type="text"
                  size="small"
                  :icon="Close"
                  @click="handleRejectComment(comment)"
                  v-permission="'comment.moderate'"
                >
                  拒绝
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  :icon="Delete"
                  @click="handleDeleteComment(comment)"
                  v-permission="'comment.delete'"
                  class="danger-button"
                >
                  删除
                </el-button>
              </div>
            </div>

            <!-- 回复评论 -->
            <div v-if="comment.replies && comment.replies.length > 0" class="comment-replies">
              <div
                v-for="reply in comment.replies"
                :key="reply.id"
                class="reply-item"
                :class="{ selected: isSelected(reply) }"
              >
                <div class="reply-card">
                  <div class="reply-header">
                    <div class="reply-checkbox" v-if="hasPermission('comment.moderate')">
                      <el-checkbox
                        :model-value="isSelected(reply)"
                        @change="handleCommentSelect(reply, $event)"
                      />
                    </div>
                    <div class="reply-author">
                      <span class="author-name">{{ reply.author?.username || '未知用户' }}</span>
                      <el-tag :type="getStatusColor(reply.status)" size="small">
                        {{ getStatusLabel(reply.status) }}
                      </el-tag>
                    </div>
                    <div class="reply-time">{{ formatDate(reply.createdAt) }}</div>
                  </div>

                  <div class="reply-body">
                    <div class="reply-text" v-html="reply.content"></div>
                  </div>

                  <div class="reply-actions">
                    <el-button
                      type="text"
                      size="small"
                      :icon="View"
                      @click="handleViewComment(reply)"
                    >
                      查看
                    </el-button>
                    <el-button
                      v-if="reply.status === 'pending'"
                      type="text"
                      size="small"
                      :icon="Check"
                      @click="handleApproveComment(reply)"
                      v-permission="'comment.moderate'"
                    >
                      通过
                    </el-button>
                    <el-button
                      v-if="reply.status === 'pending'"
                      type="text"
                      size="small"
                      :icon="Close"
                      @click="handleRejectComment(reply)"
                      v-permission="'comment.moderate'"
                    >
                      拒绝
                    </el-button>
                    <el-button
                      type="text"
                      size="small"
                      :icon="Delete"
                      @click="handleDeleteComment(reply)"
                      v-permission="'comment.delete'"
                      class="danger-button"
                    >
                      删除
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && commentList.length === 0" class="empty-state">
          <el-empty description="暂无评论数据">
            <el-button type="primary" @click="loadCommentList">
              刷新数据
            </el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
/**
 * 评论管理页面
 *
 * 功能特性：
 * - 评论列表展示和管理（文章评论、说说评论）
 * - 评论审核（通过、拒绝、删除）
 * - 批量操作（批量审核、批量删除）
 * - 筛选搜索和排序
 * - 列表视图和树形视图切换
 * - 响应式设计
 * - 权限控制
 */

import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  ChatDotRound,
  Check,
  Download,
  Operation,
  ArrowDown,
  Clock,
  CircleCheck,
  CircleClose,
  Calendar,
  ChatLineRound,
  Search,
  List,
  Share,
  View,
  Close,
  Delete
} from '@element-plus/icons-vue'
import { usePermission } from '@/composables/usePermission'
import { CommentService } from '@/services/comment'
import { formatDate } from '@/utils/date'
import type {
  Comment,
  CommentStatus,
  CommentListQuery,
  CommentStats,
  CommentTreeNode
} from '@/types/comment'
import type { User } from '@/types/user'

// ==================== 权限检查 ====================
const { hasPermission } = usePermission()

// ==================== 基础数据 ====================
const loading = ref(false)
const viewMode = ref<'list' | 'tree'>('list')
const selectedComments = ref<Comment[]>([])
const userList = ref<User[]>([])

// ==================== 分页配置 ====================
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// ==================== 筛选表单 ====================
const filterForm = reactive({
  search: '',
  status: undefined as CommentStatus | undefined,
  type: undefined as 'article' | 'post' | undefined,
  authorId: undefined as number | undefined,
  dateRange: null as [Date, Date] | null,
  sortBy: 'createdAt' as 'createdAt' | 'updatedAt' | 'author',
  sortOrder: 'DESC' as 'ASC' | 'DESC'
})

// ==================== 数据状态 ====================
const commentList = ref<Comment[]>([])
const commentTree = ref<CommentTreeNode[]>([])
const commentStats = ref<CommentStats | null>(null)

// ==================== 计算属性 ====================
/**
 * 获取状态颜色
 */
const getStatusColor = (status: CommentStatus): string => {
  const colorMap = {
    pending: 'warning',
    approved: 'success',
    rejected: 'danger'
  }
  return colorMap[status] || 'info'
}

/**
 * 获取状态标签
 */
const getStatusLabel = (status: CommentStatus): string => {
  const labelMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return labelMap[status] || '未知'
}

/**
 * 检查评论是否被选中
 */
const isSelected = (comment: Comment): boolean => {
  return selectedComments.value.some(item => item.id === comment.id)
}

// ==================== 数据加载 ====================
/**
 * 加载评论列表
 */
const loadCommentList = async () => {
  try {
    loading.value = true

    const query: CommentListQuery = {
      page: pagination.page,
      limit: pagination.limit,
      search: filterForm.search || undefined,
      status: filterForm.status,
      sortBy: filterForm.sortBy,
      sortOrder: filterForm.sortOrder
    }

    // 处理类型筛选
    if (filterForm.type === 'article') {
      query.hasArticle = true
    } else if (filterForm.type === 'post') {
      query.hasPost = true
    }

    // 处理作者筛选
    if (filterForm.authorId) {
      query.authorId = filterForm.authorId
    }

    // 处理日期范围
    if (filterForm.dateRange) {
      query.startDate = filterForm.dateRange[0].toISOString()
      query.endDate = filterForm.dateRange[1].toISOString()
    }

    const response = await CommentService.getComments(query)
    commentList.value = response.comments
    pagination.total = response.pagination.total

    // 如果是树形视图，构建评论树
    if (viewMode.value === 'tree') {
      commentTree.value = CommentService.buildCommentTree(response.comments)
    }
  } catch (error) {
    console.error('加载评论列表失败:', error)
    ElMessage.error('加载评论列表失败')
  } finally {
    loading.value = false
  }
}

/**
 * 加载统计信息
 */
const loadCommentStats = async () => {
  try {
    commentStats.value = await CommentService.getCommentStats()
  } catch (error) {
    console.error('加载统计信息失败:', error)
  }
}

/**
 * 搜索用户
 */
const searchUsers = async (query: string) => {
  if (!query) {
    userList.value = []
    return
  }

  try {
    // 这里应该调用用户搜索API
    // const users = await UserService.searchUsers({ search: query, limit: 10 })
    // userList.value = users.items || []
    userList.value = [] // 临时空数组
  } catch (error) {
    console.error('搜索用户失败:', error)
  }
}

// ==================== 事件处理 ====================
/**
 * 防抖搜索
 */
let searchTimeout: NodeJS.Timeout | null = null
const handleSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }
  searchTimeout = setTimeout(() => {
    pagination.page = 1
    loadCommentList()
  }, 300)
}

/**
 * 筛选处理
 */
const handleFilter = () => {
  pagination.page = 1
  loadCommentList()
}

/**
 * 重置筛选
 */
const handleResetFilter = () => {
  Object.assign(filterForm, {
    search: '',
    status: undefined,
    type: undefined,
    authorId: undefined,
    dateRange: null,
    sortBy: 'createdAt',
    sortOrder: 'DESC'
  })
  pagination.page = 1
  loadCommentList()
}

/**
 * 视图模式切换
 */
const handleViewModeChange = (mode: 'list' | 'tree') => {
  viewMode.value = mode
  if (mode === 'tree') {
    commentTree.value = CommentService.buildCommentTree(commentList.value)
  }
}

/**
 * 分页处理
 */
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  loadCommentList()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadCommentList()
}

// ==================== 选择操作 ====================
/**
 * 表格选择变化处理
 */
const handleSelectionChange = (selection: Comment[]) => {
  selectedComments.value = selection
}

/**
 * 评论选择处理
 */
const handleCommentSelect = (comment: Comment, selected: boolean) => {
  if (selected) {
    if (!isSelected(comment)) {
      selectedComments.value.push(comment)
    }
  } else {
    const index = selectedComments.value.findIndex(item => item.id === comment.id)
    if (index > -1) {
      selectedComments.value.splice(index, 1)
    }
  }
}

/**
 * 清空选择
 */
const clearSelection = () => {
  selectedComments.value = []
}

// ==================== 评论操作 ====================
/**
 * 查看评论详情
 */
const handleViewComment = (comment: Comment) => {
  // TODO: 实现评论详情查看功能
  ElMessage.info(`查看评论详情功能开发中: ${comment.id}`)
}

/**
 * 通过评论
 */
const handleApproveComment = async (comment: Comment) => {
  try {
    await CommentService.approveComment(comment.id)
    ElMessage.success('评论已通过')

    // 更新本地状态
    comment.status = 'approved'

    // 重新加载统计信息
    loadCommentStats()
  } catch (error) {
    console.error('通过评论失败:', error)
    ElMessage.error('通过评论失败')
  }
}

/**
 * 拒绝评论
 */
const handleRejectComment = async (comment: Comment) => {
  try {
    await CommentService.rejectComment(comment.id)
    ElMessage.success('评论已拒绝')

    // 更新本地状态
    comment.status = 'rejected'

    // 重新加载统计信息
    loadCommentStats()
  } catch (error) {
    console.error('拒绝评论失败:', error)
    ElMessage.error('拒绝评论失败')
  }
}

/**
 * 删除评论
 */
const handleDeleteComment = async (comment: Comment) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条评论吗？此操作不可撤销。`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await CommentService.deleteComment(comment.id)
    ElMessage.success('删除成功')

    // 从选中列表中移除
    const index = selectedComments.value.findIndex(item => item.id === comment.id)
    if (index > -1) {
      selectedComments.value.splice(index, 1)
    }

    // 重新加载列表
    loadCommentList()
    loadCommentStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// ==================== 批量操作 ====================
/**
 * 批量审核处理
 */
const handleBatchApprove = async () => {
  if (selectedComments.value.length === 0) {
    ElMessage.warning('请先选择要审核的评论')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要通过选中的 ${selectedComments.value.length} 条评论吗？`,
      '批量审核确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const commentIds = selectedComments.value.map(comment => comment.id)
    await CommentService.batchApproveComments(commentIds)

    ElMessage.success('批量审核成功')
    clearSelection()
    loadCommentList()
    loadCommentStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审核失败:', error)
      ElMessage.error('批量审核失败')
    }
  }
}

/**
 * 批量操作处理
 */
const handleBatchOperation = async (command: string) => {
  if (selectedComments.value.length === 0) {
    ElMessage.warning('请先选择要操作的评论')
    return
  }

  switch (command) {
    case 'approve':
      await handleBatchApprove()
      break
    case 'reject':
      await handleBatchReject()
      break
    case 'delete':
      await handleBatchDelete()
      break
  }
}

/**
 * 批量拒绝
 */
const handleBatchReject = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要拒绝选中的 ${selectedComments.value.length} 条评论吗？`,
      '批量拒绝确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const commentIds = selectedComments.value.map(comment => comment.id)
    await CommentService.batchRejectComments(commentIds)

    ElMessage.success('批量拒绝成功')
    clearSelection()
    loadCommentList()
    loadCommentStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error)
      ElMessage.error('批量拒绝失败')
    }
  }
}

/**
 * 批量删除
 */
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedComments.value.length} 条评论吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const commentIds = selectedComments.value.map(comment => comment.id)
    await CommentService.batchDeleteComments(commentIds)

    ElMessage.success('批量删除成功')
    clearSelection()
    loadCommentList()
    loadCommentStats()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// ==================== 导出功能 ====================
/**
 * 导出评论数据
 */
const handleExportComments = async () => {
  try {
    // 获取所有评论数据
    const response = await CommentService.getComments({ limit: 1000 })
    const allComments = response.comments || []

    // 准备导出数据
    const exportData = allComments.map(comment => ({
      'ID': comment.id,
      '评论内容': comment.content.replace(/<[^>]*>/g, ''), // 移除HTML标签
      '作者': comment.author?.username || '未知用户',
      '状态': getStatusLabel(comment.status),
      '类型': comment.articleId ? '文章评论' : '说说评论',
      '关联内容': comment.article?.title || '说说内容',
      '创建时间': formatDate(comment.createdAt),
      '是否回复': comment.parentId ? '是' : '否'
    }))

    // 创建CSV内容
    const headers = Object.keys(exportData[0] || {})
    const csvContent = [
      headers.join(','),
      ...exportData.map(row =>
        headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
      )
    ].join('\n')

    // 下载文件
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `评论数据_${new Date().toISOString().slice(0, 10)}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('评论数据导出成功')
  } catch (error) {
    console.error('导出评论数据失败:', error)
    ElMessage.error('导出评论数据失败')
  }
}

// ==================== 导航功能 ====================
/**
 * 查看文章
 */
const viewArticle = (articleId: number) => {
  // TODO: 实现文章查看功能
  ElMessage.info(`查看文章功能开发中: ${articleId}`)
}

/**
 * 查看说说
 */
const viewPost = (postId: number) => {
  // TODO: 实现说说查看功能
  ElMessage.info(`查看说说功能开发中: ${postId}`)
}

// ==================== 生命周期 ====================
/**
 * 组件挂载时初始化数据
 */
onMounted(() => {
  loadCommentList()
  loadCommentStats()
})
</script>

<style scoped>
.comment-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 页面头部样式 */
.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-title .el-icon {
  font-size: 32px;
}

.page-description {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

/* 统计卡片样式 */
.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  height: 100%;
}

.stats-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.rejected {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.stats-icon.today {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #666;
}

.stats-icon.replies {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  color: #666;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

/* 筛选卡片样式 */
.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.filter-content {
  padding: 20px;
}

.filter-form {
  margin-bottom: 16px;
}

.filter-form .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e4e7ed;
}

.view-toggle .el-radio-group {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 4px;
}

.batch-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
}

.selected-count {
  font-weight: 500;
}

.clear-selection {
  color: #409eff;
  padding: 0;
  font-size: 14px;
}

/* 评论展示卡片样式 */
.comment-display-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.comment-container {
  padding: 20px;
  min-height: 400px;
}

/* 列表视图样式 */
.comment-list {
  width: 100%;
}

.comment-table {
  width: 100%;
}

.comment-table .el-table__header {
  background-color: #fafbfc;
}

.comment-table .el-table__header th {
  background-color: #fafbfc !important;
  color: #606266;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
}

.comment-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.content-text {
  line-height: 1.6;
  color: #303133;
  max-height: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.content-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.reply-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  font-size: 12px;
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.author-name {
  font-weight: 600;
  color: #303133;
}

.related-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.article-link,
.post-link {
  font-size: 12px;
}

.table-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.table-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.danger-button {
  color: #f56c6c !important;
}

.danger-button:hover {
  color: #f78989 !important;
  background-color: #fef0f0 !important;
}

/* 树形视图样式 */
.comment-tree {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.comment-item {
  border: 2px solid transparent;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.comment-item.selected {
  border-color: #409eff;
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
}

.comment-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.comment-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.comment-checkbox {
  margin-right: 12px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.author-name {
  font-weight: 600;
  color: #303133;
}

.comment-time {
  font-size: 12px;
  color: #909399;
}

.comment-body {
  margin-bottom: 12px;
}

.comment-text {
  line-height: 1.6;
  color: #303133;
  margin-bottom: 8px;
}

.comment-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  font-size: 12px;
  color: #909399;
}

.related-article,
.related-post {
  display: flex;
  align-items: center;
  gap: 4px;
}

.comment-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.comment-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

/* 回复样式 */
.comment-replies {
  margin-top: 16px;
  margin-left: 32px;
  border-left: 3px solid #e4e7ed;
  padding-left: 16px;
}

.reply-item {
  margin-bottom: 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.reply-item.selected {
  border-color: #409eff;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
}

.reply-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
}

.reply-card:hover {
  background: #f0f2f5;
}

.reply-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.reply-checkbox {
  margin-right: 8px;
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.reply-time {
  font-size: 11px;
  color: #909399;
}

.reply-body {
  margin-bottom: 8px;
}

.reply-text {
  line-height: 1.5;
  color: #303133;
  font-size: 14px;
}

.reply-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.reply-actions .el-button {
  padding: 2px 6px;
  font-size: 11px;
}

/* 空状态样式 */
.empty-state {
  padding: 60px 20px;
  text-align: center;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-content {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .comment-management {
    padding: 16px;
  }

  .header-content {
    padding: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-title .el-icon {
    font-size: 28px;
  }

  .page-description {
    font-size: 14px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions .el-button {
    width: 100%;
    justify-content: center;
  }

  .stats-content {
    padding: 16px;
  }

  .stats-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
    margin-right: 12px;
  }

  .stats-value {
    font-size: 24px;
  }

  .filter-content {
    padding: 16px;
  }

  .filter-form {
    flex-direction: column;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .filter-form .el-input,
  .filter-form .el-select {
    width: 100% !important;
  }

  .view-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .comment-container {
    padding: 16px;
  }

  .comment-replies {
    margin-left: 16px;
    padding-left: 12px;
  }

  .pagination-container {
    padding: 16px 0;
  }

  .pagination-container :deep(.el-pagination) {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .comment-management {
    padding: 12px;
  }

  .header-content {
    padding: 16px;
  }

  .page-title {
    font-size: 20px;
  }

  .page-title .el-icon {
    font-size: 24px;
  }

  .stats-content {
    padding: 12px;
  }

  .stats-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
    margin-right: 8px;
  }

  .stats-value {
    font-size: 20px;
  }

  .stats-label {
    font-size: 12px;
  }

  .filter-content,
  .comment-container {
    padding: 12px;
  }

  .comment-card {
    padding: 12px;
  }

  .reply-card {
    padding: 8px;
  }

  .comment-replies {
    margin-left: 8px;
    padding-left: 8px;
  }
}

/* 下拉菜单危险项样式 */
:deep(.el-dropdown-menu__item.danger) {
  color: #f56c6c;
}

:deep(.el-dropdown-menu__item.danger:hover) {
  background-color: #fef0f0;
  color: #f56c6c;
}

/* 修复webkit-line-clamp兼容性 */
.content-text {
  -webkit-line-clamp: 3;
  line-clamp: 3;
}
</style>